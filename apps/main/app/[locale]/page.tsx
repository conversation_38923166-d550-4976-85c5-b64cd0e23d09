import type { ReactElement, FC } from 'react';
import React from 'react';
import HydratedHomepage from '@/components/homepage/hydrated-homepage';
import { useHydrateHomepage } from '@/queries/homepage';
import { HydrationBoundary, dehydrate } from '@tanstack/react-query';
import { fetchServerData } from '../utils';

const Homepage: FC = async (): Promise<ReactElement> => {
  // Pre-populate React Query cache
  const dehydratedState = await useHydrateHomepage();

  return (
    <HydrationBoundary state={dehydratedState}>
      <HydratedHomepage />
    </HydrationBoundary>
  );
};

export default Homepage;
