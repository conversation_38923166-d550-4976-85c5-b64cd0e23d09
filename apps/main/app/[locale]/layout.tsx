import '@/styles/globals.css';
import 'intl-tel-input/build/css/intlTelInput.css';
import '@/styles/intltelephone.css';
import '@/public/fonts/rnt/icons.css';
import type { Metadata } from 'next';
import Providers from '@/components/providers';
import { getLayoutDirection } from '@/lib/utils';
import { type Locale } from '@/lib/i18n/i18n-config';
import { getScopedI18n } from '@/lib/i18n/server-translator';
import { cookies } from 'next/headers';
import { GoogleAnalytics } from './GoogleAnalytics';
import Script from 'next/script';
import { fetchServerData } from '../utils';

type LayoutProps = {
  children: React.ReactNode;
  params: { locale: Locale };
};

const SITEID = process.env.NEXT_PUBLIC_HOTJAR_SITE_ID;
const HOTJAR_VERSION = process.env.NEXT_PUBLIC_HOTJAR_VERSION;

export async function generateMetadata(): Promise<Metadata> {
  const cookieStore = await cookies();
  const url = cookieStore.get('page-url')?.value;
  const t = await getScopedI18n('seo.home');

  const { data: websiteData, locale } = await fetchServerData({ url: '/v2/myrentoor' });

  if (websiteData) {
    try {
      const title = websiteData?.search_section?.name;

      const description = `${websiteData?.search_section?.brief_description || t('description')} ${websiteData?.search_section?.detail_description || ''}`;
      return {
        title: `${title} | ${t('title')}`,
        description,
        keywords: t('keywords'),
        authors: {
          name: title,
          url,
        },
        applicationName: title,
        creator: title,
        publisher: title,
        robots: 'index, follow',
        icons: [
          {
            rel: 'icon',
            url: websiteData?.favicon || '/favicon/favicon.ico',
          },
          {
            rel: 'icon',
            type: 'image/png',
            sizes: '16x16',
            url: websiteData?.favicon || '/favicon/favicon.ico',
          },
          {
            rel: 'icon',
            type: 'image/png',
            sizes: '32x32',
            url: websiteData?.favicon || '/favicon/favicon.ico',
          },
          {
            rel: 'apple-touch-icon',
            sizes: '180x180',
            url: websiteData?.favicon || '/favicon/favicon.ico',
          },
        ],
        openGraph: {
          title: `${title} | ${t('title')}`,
          description: description,
          url: url,
          siteName: `${title} | ${t('title')}`,
          type: 'website',
          images: (websiteData?.gallery ?? [])?.map((item: string) => ({
            url: item,
            alt: description,
          })),

          locale: locale === 'en' ? 'en_US' : 'ar_SA',
        },
        twitter: {
          card: 'summary_large_image',
          site: '',
          creator: '',
          title: `${title} | ${t('title')}`,
          description: description,
          images: (websiteData?.gallery ?? [])?.map((item: string) => ({
            url: item,
            alt: description,
          })),
        },
        verification: {
          google: 'your-google-site-verification-code',
        },
        // Theme and Color Settings
        themeColor: websiteData?.primary_color ?? '#d84f68',
        colorScheme: 'light',

        // Other Metadata
        alternates: {
          canonical: url,
          languages: {
            'en-US': url,
          },
        },
        manifest: `/manifest.json`,
      };        // Theme and Color Settings

        // Other Metadata

    } catch (err) {
      return {
        title: t('title'),
        description: t('description'),
        keywords: t('keywords'),
        icons: [
          {
            rel: 'icon',
            url: '/favicon.ico',
          },
        ],
      };
    }
  } else {
    return {
      title: t('title'),
      description: t('description'),
      keywords: t('keywords'),
      icons: [
        {
          rel: 'icon',
          url: '/favicon.ico',
        },
      ],
    };
  }
}

const RootLayout: React.FC<LayoutProps> = async ({ children, params: { locale } }) => {
  const dir = getLayoutDirection(locale);

  // Fetch homepage data for server-side rendering
  const { data: homepageData } = await fetchServerData({ url: '/v2/myrentoor' });

  return (
    //dir is required by tailwind to work on modifiers
    <html dir={dir} lang={locale} className="scroll-smooth">
      <head>
        {process.env.NODE_ENV === 'production' && (
          <Script id="hotjar-init" strategy="afterInteractive">
            {`
             (function(h,o,t,j,a,r){
               h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
               h._hjSettings={hjid:${SITEID},hjsv:${HOTJAR_VERSION}};
               a=o.getElementsByTagName('head')[0];
               r=o.createElement('script');r.async=1;
               r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
               a.appendChild(r);
             })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');

             window.addEventListener('load', function () {
               const domain = window.location.hostname;

               // تحديد المنصة بناءً على الدومين
               const isMyRentoor = domain.endsWith('.rentoor.info');
               const platformTag = isMyRentoor ? 'my_rentoor' : 'rentoor_main';

               // إرسال الوسوم والتعريف إلى Hotjar
               window.hj?.('tagRecording', [platformTag]);
               window.hj?.('identify', null, {
                 platform: platformTag,
                 domain: domain,
               });
             });
           `}
          </Script>
        )}
        {process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID_MAIN && (
          <GoogleAnalytics GA_MEASUREMENT_ID={process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID_MAIN} />
        )}
        <meta name="color-scheme" content="light" />
      </head>
      <body className="font-almarai">
        <Providers locale={locale} dir={dir} initialData={homepageData}>
          {children}
        </Providers>
      </body>
    </html>
  );
};

export default RootLayout;
