import { getCurrentLocale } from '@/lib/i18n/server-translator';
import { cookies } from 'next/headers';
import { parse } from 'url';

export const getTenant = async () => {
  const cookieStore = await cookies();
  const url = cookieStore.get('page-url')?.value;
  if (url && typeof url === 'string') {
    const { query, hostname } = parse(url, true);

    if (hostname && hostname !== 'localhost') {
      return hostname;
    } else {
      return query?.tenant as string | null;
    }
  } else {
    return null;
  }
};

// Cache to store previous fetch results
interface CacheEntry {
  data: any;
  timestamp: number;
  tenant: string | null;
  locale: string;
}

const cache: Record<string, CacheEntry> = {};
const CACHE_EXPIRY_TIME = 5 * 60 * 1000; // 5 minutes in milliseconds

export const fetchServerData = async ({ url, skipCache = false }: { url: string; skipCache?: boolean }) => {
  const tenant = await getTenant();
  const locale = await getCurrentLocale();

  if (!url) return { tenant, locale, data: null };

  // Create a cache key based on dependencies (URL, tenant, and locale)
  const cacheKey = [url, tenant, locale].join('_');

  // Check if we have a valid cache entry
  if (!skipCache && cache[cacheKey]) {
    const cacheEntry = cache[cacheKey];
    const now = Date.now();

    // Check if cache entry is still valid (not expired)
    if (now - cacheEntry.timestamp < CACHE_EXPIRY_TIME) {
      return { data: cacheEntry.data, tenant, locale, fromCache: true };
    }
  }

  // If no cache hit or cache expired, fetch fresh data
  const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}${url}?tenant=${tenant}`, {
    // Use revalidation instead of no-store for better performance
    next: {
      revalidate: 300, // Revalidate every 5 minutes
      tags: ['homepage-data'] // Allow for on-demand revalidation
    },
    headers: {
      'Accept-Language': locale,
    },
  });

  // Check if the response is successful before parsing JSON
  if (!response.ok) return { data: null, tenant, locale, fromCache: false, error: `API Error: ${response.status}` };

  const jsonData = await response.json();
  const websiteData = jsonData?.data;

  // Store in cache
  cache[cacheKey] = {
    timestamp: Date.now(),
    data: websiteData,
    tenant,
    locale,
  };

  return { data: websiteData, tenant, locale, fromCache: false };
};

// Function to revalidate homepage data cache
export const revalidateHomepageData = async () => {
  try {
    const { revalidateTag } = await import('next/cache');
    revalidateTag('homepage-data');
    return { success: true };
  } catch (error) {
    console.error('Failed to revalidate homepage data:', error);
    return { success: false, error };
  }
};
