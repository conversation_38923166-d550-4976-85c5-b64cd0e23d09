import axios, { AxiosError, AxiosInstance } from 'axios';
import { i18n } from '@/lib/i18n/i18n-config';
import { getAccessToken, getLang } from '@/queries/authentication/access-token';


let cachedToken: string | null | any = null;
let cachedLang: string | null | any = null;

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL ?? '';


export const API = (token = '', lang: string = i18n.defaultLocale): AxiosInstance => {
  const axiosInstance = axios.create({
    baseURL: API_URL,
    headers: {
      Authorization: `Bearer ${token}`,
      accept: 'application/json',
      'Content-Type': 'multipart/form-data',
      lang,
      'Accept-Language': lang,
    },
  });

  // Request interceptor to dynamically add token and language headers
  axiosInstance.interceptors.request.use(
    async (config) => {
      if (!cachedToken) {
        cachedToken = token || (await getAccessToken());
      }

      if (!cachedLang) {
        cachedLang = lang || (await getLang());
      }

      config.headers['Authorization'] = `Bearer ${cachedToken}`;
      config.headers['Accept-Language'] = cachedLang;
      config.headers['lang'] = cachedLang;

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Add response interceptor
  axiosInstance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const locale = await getLang();

      const language = locale || lang; // Use provided language or default

      if (!error.response) {
        console.error('Network error or no response from server');
        return Promise.reject(new Error('Network error or no response from server'));
      }

      return Promise.reject(error);
    }
  );

  return axiosInstance;
};


export const resetAPIAuthCache = () => {
  cachedToken = null;
  cachedLang = null;
};