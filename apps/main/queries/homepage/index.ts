import type { DehydratedState, UseQueryResult } from '@tanstack/react-query';
import { QueryClient, dehydrate, useQuery } from '@tanstack/react-query';
import type { AxiosResponse } from 'axios';
import { API } from '@/lib/axios/axios';
import { type Homepage, type Ratings } from '@/types/homepage';
import { getDynamicKey, keys } from '@/lib/react-query/keys';
import type { GetResponse } from '@/types/common';
import { getDomainParam } from '@/lib/utils';

export const getHomepage = async (tenant?: string): Promise<AxiosResponse<GetResponse<Homepage>>> => {
  const queryPath = `v2/myrentoor?tenant=${tenant || getDomainParam()}`;
  return API().get<GetResponse<Homepage>>(queryPath);
};

export const getRatings = async (page: number): Promise<AxiosResponse<GetResponse<Ratings>>> => {
  const queryPath = `v2/myrentoor/reviews?tenant=${getDomainParam()}&page=${page}&per_page=6`;
  return API().get<GetResponse<Ratings>>(queryPath);
};

export const useGetHomepage = (tenant?: string): UseQueryResult<AxiosResponse<GetResponse<Homepage>>> => {
  return useQuery({
    queryKey: keys.homepage,
    queryFn: () => getHomepage(tenant),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnMount: false, // Don't refetch on mount if data exists
  });
};

export const useGetRatings = (page: number): UseQueryResult<AxiosResponse<GetResponse<Ratings>>> => {
  return useQuery({
    queryKey: getDynamicKey('ratings', [page.toString()]),
    queryFn: () => getRatings(page),
    placeholderData: (previousData) => previousData,
  });
};

export const useHydrateHomepage = async (tenant?: string): Promise<DehydratedState> => {
  const queryClient = new QueryClient();
  await queryClient.prefetchQuery({
    queryKey: keys.homepage,
    queryFn: () => getHomepage(tenant),
  });
  return dehydrate(queryClient);
};

export const getIsOneUnitClass = (homepage: Homepage | undefined): boolean => {
  if (homepage === undefined) {
    return false;
  }
  return (
    homepage.classification_type === 'one_unit' ||
    homepage.classification_type === 'identical_units' ||
    (Array.isArray(homepage.classifications) && homepage.classifications.length === 1)
  );
};
