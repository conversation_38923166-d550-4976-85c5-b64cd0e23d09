'use client';
import { createContext, type ReactElement, useMemo, useState, useEffect } from 'react';
import { formatToHsl, generateShades, hexToHsl } from '@/lib/color/color';
import { Homepage } from '@/types/homepage';
import { getDomainParam } from '@/lib/utils';
import NotFondLayout from '@/components/layouts/notFond-layout';
import { NotFoundView } from '@/components/error';
import useClientOnly from '@/hooks/use-client-only';
import { useCurrentLocale } from '@/lib/i18n/client-translator';
import { useGetHomepage } from '@/queries/homepage';

const defaultPrimaryColor = '#D84E67';
const defaultSecondaryColor = '#335C87';

interface ServerDataContextType {
  primaryColor: string;
  secondaryColor: string;
  loading: boolean;
  data: Homepage;
  error: boolean;
}

export const ServerDataContext = createContext<ServerDataContextType>({
  primaryColor: defaultPrimaryColor,
  secondaryColor: defaultSecondaryColor,
  loading: true,
  data: {} as Homepage,
  error: false,
});

interface ServerDataProviderProps {
  children: React.ReactNode;
  initialData?: Homepage;
}

const ServerDataProvider = ({ children, initialData }: ServerDataProviderProps): ReactElement => {
  const [error, setError] = useState(false);
  const isClient = useClientOnly({ setStateValue: true });
  
  // Use React Query with server-side hydration
  const { data: queryData, isLoading, isError } = useGetHomepage();
  
  // Use initial data if available, otherwise fall back to React Query data
  const data = initialData || queryData?.data?.data || ({} as Homepage);
  const loading = !initialData && isLoading;

  useEffect(() => {
    if (isError) {
      setError(true);
    }
  }, [isError]);

  const colors = useMemo(() => {
    const primaryColor = data?.primary_color ?? defaultPrimaryColor;
    const secondaryColor = data?.secondary_color ?? defaultSecondaryColor;
    const primary = generateShades(hexToHsl(primaryColor));
    const secondary = generateShades(hexToHsl(secondaryColor));
    return {
      primary,
      secondary,
      primaryColor,
      secondaryColor,
    };
  }, [data]);

  if (error) {
    return (
      <NotFondLayout>
        <NotFoundView />
      </NotFondLayout>
    );
  }

  return (
    <ServerDataContext.Provider
      value={{
        primaryColor: colors.primaryColor,
        secondaryColor: colors.secondaryColor,
        loading: loading,
        data: data as Homepage,
        error: error,
      }}>
      {data && isClient && (
        <style>
          {':root {'}
          {Object.keys(colors.primary).map(
            (shade: any) => `--primary-${shade}: ${formatToHsl(colors.primary[shade])};`
          )}
          {Object.keys(colors.secondary).map(
            (shade: any) => `--secondary-${shade}: ${formatToHsl(colors.secondary[shade])};`
          )}
          {'}'}
        </style>
      )}
      {data && isClient && (
        <head>
          <link rel="icon" href={data?.favicon} type="image/png" />
        </head>
      )}

      {children}
    </ServerDataContext.Provider>
  );
};

export default ServerDataProvider;
