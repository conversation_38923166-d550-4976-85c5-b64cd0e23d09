'use client';
import type { FC, PropsWithChildren, ReactElement } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { DirectionProvider } from '@radix-ui/react-direction';
import getQueryClient from '@/lib/react-query/get-query-client';
import { I18nProviderClient } from '@/lib/i18n/client-translator';
import { type Direction } from '@/types/common';
import { Toaster } from '@/components/ui/sonner';
import ServerDataProvider from '@/contexts/server-data-context';
import LoginModal from './header/login-modal';
import AuthWrapper from '@/contexts/auth-context';
import { Homepage } from '@/types/homepage';

type ProvidersProps = {
  locale: string;
  dir: Direction;
  initialData?: Homepage;
} & PropsWithChildren;

const Providers: FC<ProvidersProps> = ({ locale, dir, children, initialData }): ReactElement => {
  const queryClient = getQueryClient();

  return (
    <QueryClientProvider client={queryClient}>
      <I18nProviderClient locale={locale}>
        <DirectionProvider dir={dir}>
          <ServerDataProvider initialData={initialData}>
            <AuthWrapper>
              {children}
              <LoginModal />
            </AuthWrapper>
            <Toaster />
          </ServerDataProvider>
        </DirectionProvider>
        <ReactQueryDevtools initialIsOpen={false} />
      </I18nProviderClient>
    </QueryClientProvider>
  );
};
export default Providers;
