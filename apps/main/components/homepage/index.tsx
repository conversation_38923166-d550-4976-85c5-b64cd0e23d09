'use client';
import type { FC, ReactElement } from 'react';
import React, { useEffect, useState } from 'react';
import type { DateRange } from 'react-day-picker';
import SearchSection from '@/components/homepage/search-bar/search-section';
import Classifications from '@/components/homepage/classification';
import ClientReview from '@/components/homepage/client-review';
import { getIsOneUnitClass } from '@/queries/homepage';
import Location from '@/components/homepage/location';
import Policy from '@/components/homepage/policy';
import Gallery from '@/components/homepage/gallery';
import Amenities from '@/components/homepage/amenities';
import CustomImage from '../common/custom-image';
import { getLogoFromLogos } from '@/lib/utils';
import { Separator } from '../ui/separator';
import { SectionsIds } from '@/constants';
import HomePageSkeleton from './home-page-skeleton';
import useServerHomePageData from '@/hooks/use-server-homepage-data';
import { useSearchParams } from 'next/navigation';
const Homepage: FC = (): ReactElement => {
  const [date, setDate] = useState<DateRange | undefined>();
  const [guests, setGuests] = useState<number | undefined>();
  const { loading, data } = useServerHomePageData();
  const isOneUnitClass = getIsOneUnitClass(data);
  const searchParams = useSearchParams();

  const classificationsArray = Array.isArray(data?.classifications)
    ? data?.classifications
    : data?.classifications
      ? [data?.classifications]
      : [];

  const scrollTo = searchParams.get('scrollTo');

  useEffect(() => {
    if (scrollTo) {
      const targetElement = document.getElementById(scrollTo);
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    }
  }, [scrollTo]);

  return (
    <>
      {loading ? (
        <HomePageSkeleton />
      ) : (
        <>
          <section id={SectionsIds.search}>
            <div className="grid grid-cols-12 lg:gap-x-4">
              <div className="order-2 col-span-12 lg:order-1 lg:col-span-5">
                <div className="relative h-full">
                  <div className="flex h-full flex-col-reverse justify-center lg:flex-col lg:gap-y-5">
                    <div>
                      <h1 className="text-secondary-300 sm:headline-2 mb-3 text-5xl font-bold">
                        {data?.search_section.name}
                      </h1>
                      <p className="sub-headline-xs text-black-300  text-justify leading-8">
                        {data?.search_section.brief_description}
                      </p>
                    </div>
                    <div className="xs:px-4 relative   z-10 -translate-y-1/2 transform px-2 lg:translate-y-0 lg:px-0 xl:-me-[132px] ">
                      <SearchSection
                        className="w-full lg:w-[580px] xl:w-[650px]"
                        date={date}
                        guests={guests}
                        setDate={setDate}
                        setGuests={setGuests}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="order-1 col-span-12 lg:order-2  lg:col-span-7">
                <div className="relative aspect-[761/562] w-full overflow-hidden rounded-lg">
                  <CustomImage
                    fill
                    className=" h-[237px] rounded-lg object-contain  sm:h-[450px] lg:h-[562px]"
                    alt={data?.search_section.name}
                    src={getLogoFromLogos(data?.search_section.cover_image)}
                  />
                </div>
              </div>
            </div>
            <div className="lg:mt-10">
              <p className="sub-headline-xs text-justify text-black " id="detail_description">
                {data?.search_section.detail_description}
              </p>
              <div className="my-8">
                <div className="mx-auto h-[1px] w-1/2">
                  <Separator orientation="horizontal" />
                </div>
              </div>
            </div>
          </section>

          {!isOneUnitClass ? (
            classificationsArray?.length === 1 ? (
              <Amenities items={data?.classifications?.amenities} />
            ) : (
              <Classifications
                classifications={classificationsArray.slice(0, 4)}
                classificationsCount={classificationsArray.length}
              />
            )
          ) : (
            <Amenities items={data?.classifications?.amenities} />
          )}
          <div className="section-padding-top">
            <Gallery images={data?.gallery} />
          </div>
          <div className="section-padding-top">
            <Location location={data?.location} />
          </div>
          {data?.reviews && (
            <div className="section-padding-top">
              <ClientReview reviews={data?.reviews} />
            </div>
          )}

          <div className="section-padding-top">
            <Policy cancellationPolicy={data?.cancellation_policy.policy} payType={data?.pay_type} rule={data?.rule} />
          </div>
        </>
      )}
    </>
  );
};
export default Homepage;
